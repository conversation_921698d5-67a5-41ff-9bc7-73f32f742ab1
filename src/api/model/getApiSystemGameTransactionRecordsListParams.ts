/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */

export type GetApiSystemGameTransactionRecordsListParams = {
/**
 * Transaction ID filter
 */
transactionId?: string;
/**
 * User ID filter
 */
userId?: number;
/**
 * Game catalog ID filter
 */
gameCatalogId?: number;
/**
 * Provider code filter (EG5/PG/PP)
 */
providerCode?: string;
/**
 * Game code filter
 */
gameCode?: string;
/**
 * Transaction type filter (bet/win/refund/cancel)
 */
type?: string;
/**
 * Transaction status filter (success/failed/pending)
 */
status?: string;
/**
 * Currency filter
 */
currency?: string;
/**
 * Commission status filter (pending/processed/failed)
 */
commissionStatus?: string;
/**
 * Betting bonus status filter (unprocessed/processed/not_applicable)
 */
bettingBonusStatus?: string;
/**
 * Minimum amount filter
 */
amountMin?: string;
/**
 * Maximum amount filter
 */
amountMax?: string;
/**
 * Start date filter (YYYY-MM-DD)
 */
dateStart?: string;
/**
 * End date filter (YYYY-MM-DD)
 */
dateEnd?: string;
/**
 * Search keyword for game name, transaction ID, or user ID
 */
keyword?: string;
/**
 * Filter by Telegram ID
 */
telegramId?: number;
/**
 * Filter by Telegram username
 */
telegramUsername?: string;
/**
 * Filter by first name
 */
firstName?: string;
/**
 * Filter by tenant username
 */
tenantUsername?: string;
/**
 * 租户业务名称 (模糊搜索)
 */
tenantBusinessName?: string;
/**
 * Page number
 */
page?: number;
/**
 * Page size
 */
pageSize?: number;
/**
 * Export format (excel/csv), if provided will export instead of paginate
 */
exportFormat?: string;
};
