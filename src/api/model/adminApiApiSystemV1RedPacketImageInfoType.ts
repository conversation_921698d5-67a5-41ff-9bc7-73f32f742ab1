/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */

export interface AdminApiApiSystemV1RedPacketImageInfoType {
  /** 红包 ID (主键) */
  redPacketImagesId?: number;
  /** 租户 id */
  tenantId?: number;
  /** 图片状态 pending_review, fail, success */
  status?: string;
  /** 处理状态: pending-待处理, processing-处理中, completed-已完成,    failed-处理失败 */
  processingStatus?: string;
  /** 处理尝试次数 */
  processingAttempts?: number;
  /** 开始处理时间 */
  processingStartedAt?: string;
  /** 处理完成时间 */
  processingCompletedAt?: string;
  /** 处理错误信息 */
  processingError?: string;
  /** 原始文件大小(字节) */
  originalFileSize?: number;
  /** 处理后文件大小(字节) */
  processedFileSize?: number;
  /** 图片宽度 */
  imageWidth?: number;
  /** 图片高度 */
  imageHeight?: number;
  /** 图片格式(jpeg, png等) */
  imageFormat?: string;
  /** 创建时间 */
  createdAt?: string;
  /** 软删除的时间戳 */
  deletedAt?: string;
  /** 最后更新时间 */
  updatedAt?: string;
  /** 拒绝原因 (中文) */
  refuseReasonZh?: string;
  /** 拒绝原因 (英文) */
  refuseReasonEn?: string;
  /** 用户ID (Foreign key to users table recommended) */
  userId?: number;
  imagesUrl?: string;
  fileId?: string;
  /** 是否已发送通知: 0-未发送, 1-已发送 */
  notificationSent?: number;
  /** 通知发送时间 */
  notificationSentAt?: string;
  /** 用户名 */
  username?: string;
  /** 一级代理名称 */
  firstAgentName?: string;
  /** 二级代理名称 */
  secondAgentName?: string;
  /** 三级代理名称 */
  thirdAgentName?: string;
  /** 主Telegram ID */
  telegramId?: string;
  /** 主Telegram用户名 */
  telegramUsername?: string;
  /** 主备份账户名字 */
  firstName?: string;
  /** 租户用户名 */
  tenantUsername?: string;
  /** 租户业务名称 */
  tenantBusinessName?: string;
  /** 租户名称 */
  tenantName?: string;
  /** 租户状态 (0-禁用, 1-启用) */
  tenantStatus?: number;
}
