/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */

export interface AdminApiApiSystemV1GameTransactionRecordsListItem {
  /** Record ID */
  id?: number;
  /** Transaction unique identifier */
  transactionId?: string;
  /** User ID */
  userId?: number;
  /** Game catalog ID */
  gameCatalogId?: number;
  /** Game name snapshot */
  snapshotGameName?: string;
  /** Game type snapshot */
  snapshotGameType?: string;
  /** Game provider code */
  providerCode?: string;
  /** Provider game code */
  gameCode?: string;
  /** Session ID */
  sessionId?: string;
  /** Transaction type */
  type?: string;
  /** Transaction status */
  status?: string;
  /** Currency type */
  currency?: string;
  /** Transaction amount */
  amount?: string;
  /** Win amount */
  winAmount?: string;
  /** Net amount (win-loss) */
  netAmount?: string;
  /** Balance before transaction */
  balanceBefore?: string;
  /** Balance after transaction */
  balanceAfter?: string;
  /** Provider transaction ID */
  providerTransactionId?: string;
  /** Reference transaction ID */
  referenceId?: string;
  /** Game round ID */
  roundId?: string;
  /** Bet ID */
  betId?: string;
  /** Commission processing status */
  commissionStatus?: string;
  /** Betting bonus processing status */
  bettingBonusStatus?: string;
  /** Creation time */
  createdAt?: string;
  /** Update time */
  updatedAt?: string;
  /** Telegram ID */
  telegramId?: number;
  /** Telegram username */
  telegramUsername?: string;
  /** First name */
  firstName?: string;
  /** Tenant username */
  tenantUsername?: string;
  /** 租户业务名称 */
  tenantBusinessName?: string;
  /** 租户名称 */
  tenantName?: string;
  /** 租户状态 (0-禁用, 1-启用) */
  tenantStatus?: number;
}
