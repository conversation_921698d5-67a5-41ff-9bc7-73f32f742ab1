/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */

export interface AdminApiApiSystemV1ListAdminTransfersReq {
  /** 页码 */
  page: number;
  /** 每页数量 */
  pageSize: number;
  /** 是否导出: 0不导出, 1导出 */
  export?: number;
  /** 发送方用户ID */
  senderUserId?: number;
  /** 发送方用户名(模糊查询) */
  senderUsername?: string;
  /** 接收方用户ID */
  receiverUserId?: number;
  /** 接收方用户名(模糊查询) */
  receiverUsername?: string;
  /** 代币ID */
  tokenId?: number;
  /** 代币符号(模糊查询) */
  tokenSymbol?: string;
  /** 日期范围 (YYYY-MM-DD,YYYY-MM-DD) */
  dateRange?: string;
  /** 最小金额 */
  amountMin?: string;
  /** 最大金额 */
  amountMax?: string;
  /** 转账ID */
  transferId?: number;
  /** 转账状态 */
  status?: number;
  /** 发送方账户(模糊查询) */
  senderAccount?: string;
  /** 接收方账户(模糊查询) */
  receiverAccount?: string;
  /** 一级代理名称 (模糊搜索) */
  firstAgentName?: string;
  /** 二级代理名称 (模糊搜索) */
  secondAgentName?: string;
  /** 三级代理名称 (模糊搜索) */
  thirdAgentName?: string;
  /** Telegram ID (模糊搜索) */
  telegramId?: string;
  /** Telegram用户名 (模糊搜索) */
  telegramUsername?: string;
  /** 真实姓名 (模糊搜索) */
  firstName?: string;
  /** 租户用户名 (模糊搜索) */
  tenantUsername?: string;
  /** 租户业务名称 (模糊搜索) */
  businessName?: string;
  /** 转账密钥/唯一标识(精确查询) */
  key?: string;
}
