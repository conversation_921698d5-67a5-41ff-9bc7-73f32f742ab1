/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */

export type GetApiSystemUserAddressesParams = {
/**
 * 页码
 */
page?: number;
/**
 * 每页数量
 */
pageSize?: number;
/**
 * 用户ID
 */
userId?: number;
/**
 * 用户账号
 */
account?: string;
/**
 * 币种ID
 */
tokenId?: number;
/**
 * 币种名称 (模糊查询)
 */
symbol?: string;
/**
 * 链名称
 */
chan?: string;
/**
 * 地址 (模糊查询)
 */
address?: string;
/**
 * 地址类型
 */
type?: string;
/**
 * 创建时间范围，格式：2025-01-01,2025-01-31
 */
dateRange?: string;
/**
 * 一级代理名称 (模糊搜索)
 */
firstAgentName?: string;
/**
 * 二级代理名称 (模糊搜索)
 */
secondAgentName?: string;
/**
 * 三级代理名称 (模糊搜索)
 */
thirdAgentName?: string;
/**
 * Telegram ID (模糊搜索)
 */
telegramId?: string;
/**
 * Telegram用户名 (模糊搜索)
 */
telegramUsername?: string;
/**
 * 名字 (模糊搜索)
 */
firstName?: string;
/**
 * 租户用户名 (模糊搜索)
 */
tenantUsername?: string;
/**
 * 租户业务名称 (模糊搜索)
 */
businessName?: string;
};
