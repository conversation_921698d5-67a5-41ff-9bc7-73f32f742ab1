/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */
import type { GetApiSystemUserRechargesState } from './getApiSystemUserRechargesState';

export type GetApiSystemUserRechargesParams = {
/**
 * 页码
 */
page?: number;
/**
 * 每页数量
 */
pageSize?: number;
/**
 * 是否导出：0不导出，1导出
 */
export?: number;
/**
 * 用户ID
 */
userId?: number;
/**
 * 用户账号
 */
account?: string;
/**
 * 币种ID
 */
tokenId?: number;
/**
 * 币种名称 (模糊查询)
 */
name?: string;
/**
 * 链名称
 */
chan?: string;
/**
 * 来源地址
 */
fromAddress?: string;
/**
 * 目标地址
 */
toAddress?: string;
/**
 * 交易哈希
 */
txHash?: string;
/**
 * 1:待确认/处理中, 2:已完成/已入账 3.撤销
 */
state?: GetApiSystemUserRechargesState;
/**
 * 创建日期范围 (YYYY-MM-DD,YYYY-MM-DD)
 */
dateRange?: string;
/**
 * 一级代理名称 (模糊搜索)
 */
firstAgentName?: string;
/**
 * 二级代理名称 (模糊搜索)
 */
secondAgentName?: string;
/**
 * 三级代理名称 (模糊搜索)
 */
thirdAgentName?: string;
/**
 * Telegram ID (模糊搜索)
 */
telegramId?: string;
/**
 * Telegram用户名 (模糊搜索)
 */
telegramUsername?: string;
/**
 * 名字 (模糊搜索)
 */
firstName?: string;
/**
 * 租户用户名 (模糊搜索)
 */
tenantUsername?: string;
/**
 * 租户业务名称 (模糊搜索)
 */
businessName?: string;
};
