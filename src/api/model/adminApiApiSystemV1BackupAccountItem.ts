/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */

export interface AdminApiApiSystemV1BackupAccountItem {
  /** 关联ID */
  id?: number;
  /** 主用户ID */
  aUserId?: number;
  /** 备用用户ID */
  bUserId?: number;
  /** 备用用户Telegram用户名 */
  telegramUsername?: string;
  /** 备用用户Telegram ID */
  telegramId?: number;
  /** 创建时间 */
  createdAt?: string;
  /** 更新时间 */
  updatedAt?: string;
  /** 验证时间 */
  verifiedAt?: string;
  /** 主用户Telegram ID */
  mainTelegramId?: string;
  /** 主用户Telegram用户名 */
  mainTelegramUsername?: string;
  /** 主用户真实姓名 */
  firstName?: string;
  /** 租户用户名 */
  tenantUsername?: string;
  /** 租户业务名称 */
  tenantBusinessName?: string;
  /** 租户名称 */
  tenantName?: string;
  /** 租户状态 (0-禁用, 1-启用) */
  tenantStatus?: number;
}
