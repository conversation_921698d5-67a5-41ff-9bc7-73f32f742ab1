/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */
import type { GithubComShopspringDecimalDecimal } from './githubComShopspringDecimalDecimal';
import type { Interface } from './interface';

export interface AdminApiApiSystemV1GetGameRngBetDetailsDetailRes {
  /** Bet details ID */
  id?: number;
  /** Game account username */
  username?: string;
  /** Bet amount */
  betAmount?: GithubComShopspringDecimalDecimal;
  /** Valid bet amount */
  validBetAmount?: GithubComShopspringDecimalDecimal;
  /** Win amount */
  winAmount?: GithubComShopspringDecimalDecimal;
  /** Net profit and loss (positive=win, negative=loss) */
  netPnl?: GithubComShopspringDecimalDecimal;
  /** Currency (CNY, USD, etc.) */
  currency?: string;
  /** Game code */
  gameCode?: string;
  /** Game name (RNG/FISH specific field) */
  gameName?: string;
  /** Product type (16=RNG, others=FISH) */
  productType?: number;
  /** Game category (RNG, FISH) */
  gameCategory?: string;
  /** Bet order number */
  betOrderNo?: string;
  /** Session identifier */
  sessionId?: string;
  /** Bet time */
  betTime?: string;
  /** Transaction time */
  transactionTime?: string;
  additionalDetails?: Interface;
  /** API response status code */
  apiStatus?: number;
  /** API error description */
  apiErrorDesc?: string;
  /** Record creation time */
  createdAt?: string;
  /** Last update time */
  updatedAt?: string;
  /** Telegram ID */
  telegramId?: number;
  /** Telegram username */
  telegramUsername?: string;
  /** First name */
  firstName?: string;
  /** Tenant username */
  tenantUsername?: string;
  /** 租户业务名称 */
  tenantBusinessName?: string;
  /** 租户名称 */
  tenantName?: string;
  /** 租户状态 (0-禁用, 1-启用) */
  tenantStatus?: number;
}
