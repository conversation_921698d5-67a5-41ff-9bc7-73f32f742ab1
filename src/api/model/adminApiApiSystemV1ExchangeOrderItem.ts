/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */
import type { GithubComShopspringDecimalDecimal } from './githubComShopspringDecimalDecimal';

export interface AdminApiApiSystemV1ExchangeOrderItem {
  /** 订单ID */
  orderId?: number;
  /** 订单号 */
  orderSn?: string;
  /** 用户ID */
  userId?: number;
  /** 产品ID */
  productId?: number;
  /** 基础代币 */
  baseToken?: string;
  /** 计价代币 */
  quoteToken?: string;
  /** 交易对符号 */
  symbol?: string;
  /** 交易类型 */
  tradeType?: string;
  /** 基础代币数量（截断后） */
  amountBase?: GithubComShopspringDecimalDecimal;
  /** 计价代币数量（截断后） */
  amountQuote?: GithubComShopspringDecimalDecimal;
  /** 原始兑换金额（未截断） */
  originalFromAmount?: GithubComShopspringDecimalDecimal;
  /** 原始收到金额（未截断） */
  originalToAmount?: GithubComShopspringDecimalDecimal;
  /** 成交价格 */
  price?: GithubComShopspringDecimalDecimal;
  /** 点差金额 */
  spreadAmount?: GithubComShopspringDecimalDecimal;
  /** 点差率 */
  spreadRate?: GithubComShopspringDecimalDecimal;
  /** 手续费金额 */
  feeAmount?: GithubComShopspringDecimalDecimal;
  /** 手续费币种ID */
  feeTokenId?: number;
  /** 扣费前输出金额 */
  outputAmountBeforeFee?: GithubComShopspringDecimalDecimal;
  /** 扣费后输出金额 */
  outputAmountAfterFee?: GithubComShopspringDecimalDecimal;
  /** 手续费计算方法 */
  feeCalculationMethod?: string;
  /** 交易哈希 */
  transactionHash?: string;
  /** 关联的报价ID */
  quoteId?: string;
  /** 订单状态 */
  status?: string;
  /** 错误信息 */
  errorMessage?: string;
  /** 客户端订单ID */
  clientOrderId?: string;
  /** 创建时间 */
  createdAt?: string;
  /** 实际执行时间 */
  executedAt?: string;
  /** 完成时间 */
  completedAt?: string;
  /** 更新时间 */
  updatedAt?: string;
  /** 用户账号 */
  account?: string;
  /** 一级代理名称 */
  firstAgentName?: string;
  /** 二级代理名称 */
  secondAgentName?: string;
  /** 三级代理名称 */
  thirdAgentName?: string;
  /** 主Telegram ID */
  telegramId?: string;
  /** 主Telegram用户名 */
  telegramUsername?: string;
  /** 主备份账户名字 */
  firstName?: string;
  /** 租户用户名 */
  tenantUsername?: string;
  /** 租户业务名称 */
  tenantBusinessName?: string;
  /** 租户名称 */
  tenantName?: string;
  /** 租户状态 (0-禁用, 1-启用) */
  tenantStatus?: number;
}
