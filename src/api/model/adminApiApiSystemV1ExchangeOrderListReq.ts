/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */

export interface AdminApiApiSystemV1ExchangeOrderListReq {
  /** 订单号 */
  orderSn?: string;
  /** 用户ID */
  userId?: number;
  /** 产品ID */
  productId?: number;
  /** 交易对符号 */
  symbol?: string;
  /** 交易类型(buy/sell) */
  tradeType?: string;
  /** 订单状态 */
  status?: string;
  /** 创建时间范围，格式：2025-01-01,2025-01-31 */
  dateRange?: string;
  /** 是否导出:0不导出,1导出 */
  export?: number;
  /** 页码 */
  page?: number;
  /** 每页数量 */
  pageSize?: number;
  /** 一级代理名称 (模糊搜索) */
  firstAgentName?: string;
  /** 二级代理名称 (模糊搜索) */
  secondAgentName?: string;
  /** 三级代理名称 (模糊搜索) */
  thirdAgentName?: string;
  /** Telegram ID (模糊搜索) */
  telegramId?: string;
  /** Telegram用户名 (模糊搜索) */
  telegramUsername?: string;
  /** 真实姓名 (模糊搜索) */
  firstName?: string;
  /** 租户用户名 (模糊搜索) */
  tenantUsername?: string;
  /** 租户业务名称 (模糊搜索) */
  businessName?: string;
}
