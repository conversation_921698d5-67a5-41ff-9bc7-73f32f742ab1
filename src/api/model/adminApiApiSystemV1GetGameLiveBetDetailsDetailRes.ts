/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */
import type { GithubComShopspringDecimalDecimal } from './githubComShopspringDecimalDecimal';
import type { GithubComGogfGfV2EncodingGjsonJson } from './githubComGogfGfV2EncodingGjsonJson';

export interface AdminApiApiSystemV1GetGameLiveBetDetailsDetailRes {
  /** Record ID */
  id?: number;
  /** Game account username */
  username?: string;
  /** Bet amount */
  betAmount?: GithubComShopspringDecimalDecimal;
  /** Valid bet amount */
  validBetAmount?: GithubComShopspringDecimalDecimal;
  /** Win amount */
  winAmount?: GithubComShopspringDecimalDecimal;
  /** Net profit/loss (positive for win, negative for loss) */
  netPnl?: GithubComShopspringDecimalDecimal;
  /** Currency (e.g., CNY, USD) */
  currency?: string;
  /** Game code */
  gameCode?: string;
  /** Product type (191=Live game) */
  productType?: number;
  /** Game category */
  gameCategory?: string;
  /** Bet order number */
  betOrderNo?: string;
  /** Session ID */
  sessionId?: string;
  /** Bet time */
  betTime?: string;
  /** Transaction time */
  transactionTime?: string;
  /** Additional bet details */
  additionalDetails?: GithubComGogfGfV2EncodingGjsonJson;
  /** API response status code */
  apiStatus?: number;
  /** API error description */
  apiErrorDesc?: string;
  /** Creation time */
  createdAt?: string;
  /** Update time */
  updatedAt?: string;
  /** Telegram ID */
  telegramId?: number;
  /** Telegram username */
  telegramUsername?: string;
  /** First name */
  firstName?: string;
  /** Tenant username */
  tenantUsername?: string;
  /** 租户业务名称 */
  tenantBusinessName?: string;
  /** 租户名称 */
  tenantName?: string;
  /** 租户状态 (0-禁用, 1-启用) */
  tenantStatus?: number;
}
