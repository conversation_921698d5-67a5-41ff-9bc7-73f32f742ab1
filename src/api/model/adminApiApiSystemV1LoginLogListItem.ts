/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */
import type { GithubComGogfGfV2EncodingGjsonJson } from './githubComGogfGfV2EncodingGjsonJson';

export interface AdminApiApiSystemV1LoginLogListItem {
  /** 日志ID */
  id?: number;
  /** 租户 id */
  tenantId?: number;
  /** 请求ID */
  reqId?: string;
  /** 用户类型 */
  memberType?: string;
  /** 用户ID */
  memberId?: number;
  /** 用户名 */
  username?: string;
  /** 响应数据 */
  response?: GithubComGogfGfV2EncodingGjsonJson;
  /** 登录时间 */
  loginAt?: string;
  /** 登录IP */
  loginIp?: string;
  /** 省编码 */
  provinceId?: number;
  /** 市编码 */
  cityId?: number;
  /** UA信息 */
  userAgent?: string;
  /** 错误提示 */
  errMsg?: string;
  /** 状态 */
  status?: number;
  /** 创建时间 */
  createdAt?: string;
  /** 修改时间 */
  updatedAt?: string;
  /** 软删除的时间戳 */
  deletedAt?: string;
  /** 用户账号 */
  account?: string;
  /** Telegram用户名 */
  telegramUsername?: string;
  /** Telegram ID */
  telegramId?: number;
  /** 一级代理名称 */
  firstAgentName?: string;
  /** 二级代理名称 */
  secondAgentName?: string;
  /** 三级代理名称 */
  thirdAgentName?: string;
  /** 租户用户名 */
  tenantUsername?: string;
  /** 租户业务名称 */
  tenantBusinessName?: string;
  /** 租户名称 */
  tenantName?: string;
  /** 租户状态 (0-禁用, 1-启用) */
  tenantStatus?: number;
}
