/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */

export type PutApiSystemMerchantsMerchantIdBody = {
  /**
   * 商户名称（只能包含字母、数字、下划线或连字符）
   * @minLength 2
   * @maxLength 150
   */
  merchantName: string;
  /**
   * 公司/业务注册名称 (可选)
   * @maxLength 255
   */
  businessName?: string;
  /** 商户邮箱 */
  email: string;
  /**
   * 商户网站URL (可选)
   * @maxLength 255
   */
  websiteUrl?: string;
  /**
   * 备注
   * @maxLength 200
   */
  notes?: string;
  /**
   * 联系电话
   * @maxLength 50
   */
  phone?: string;
  /** 备用联系邮箱 */
  contactEmail?: string;
  /**
   * 回调URL
   * @maxLength 512
   */
  callbackUrl?: string;
};
