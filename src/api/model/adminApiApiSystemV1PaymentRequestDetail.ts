/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */
import type { GithubComShopspringDecimalDecimal } from './githubComShopspringDecimalDecimal';

export interface AdminApiApiSystemV1PaymentRequestDetail {
  /** 收款请求 ID (主键) */
  requestId?: number;
  /** 租户 id */
  tenantId?: number;
  /** 收款发起者用户 ID (外键, 指向 users.user_id) */
  requesterUserId?: number;
  /** 收款发起者用户名 */
  requesterUsername?: string;
  /** 指定付款人用户 ID (如果为空则任何人可付, 外键, 指向 users.user_id) */
  payerUserId?: number;
  /** 付款人用户名 */
  payerUsername?: string;
  /** 收款代币 ID (外键, 指向 tokens.token_id) */
  tokenId?: number;
  /** 收款金额 */
  amount?: GithubComShopspringDecimalDecimal;
  /** 收款说明/备注 */
  memo?: string;
  /** 请求状态: 1-待支付(pending), 2-已支付(paid), 3-已过期(expired), 4-已取消(cancelled) */
  status?: number;
  /** 关联的支付交易记录ID (外键, 指向 transactions.transaction_id) */
  paymentTransactionId?: number;
  /** 关联的支付交易记录ID (外键, 指向 transactions.transaction_id) */
  requesterTransactionId?: number;
  /** 发起请求的 Telegram 聊天 ID (用于更新消息) */
  telegramChatId?: number;
  /** 原始收款请求消息的 Telegram 消息 ID (用于更新消息) */
  telegramMessageId?: number;
  /** 创建时间 */
  createdAt?: string;
  /** 过期时间 (例如: 创建时间 + 24小时) */
  expiresAt?: string;
  /** 支付时间 */
  paidAt?: string;
  /** 取消时间 */
  cancelledAt?: string;
  /** 内联消息 ID，用于后续编辑 */
  inlineMessageId?: string;
  /** 最后更新时间 */
  updatedAt?: string;
  /** 软删除的时间戳 */
  deletedAt?: string;
  symbol?: string;
  /** 代币符号 */
  tokenSymbol?: string;
  /** 代币名称 */
  tokenName?: string;
  /** 状态文本 */
  statusText?: string;
  /** 关联交易ID */
  transactionId?: number;
  /** 交易状态 */
  transactionStatus?: number;
  /** 交易时间 */
  transactionTime?: string;
}
