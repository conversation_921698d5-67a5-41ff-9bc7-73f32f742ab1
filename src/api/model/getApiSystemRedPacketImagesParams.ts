/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */
import type { GetApiSystemRedPacketImagesStatus } from './getApiSystemRedPacketImagesStatus';

export type GetApiSystemRedPacketImagesParams = {
page: number;
pageSize: number;
/**
 * 状态: pending_review-待审核, success-通过, fail-拒绝
 */
status?: GetApiSystemRedPacketImagesStatus;
/**
 * 用户ID
 */
userId?: number;
/**
 * 创建时间范围，格式：2025-01-01,2025-01-31
 */
dateRange?: string;
/**
 * 是否导出
 */
export?: boolean;
/**
 * 一级代理名称 (模糊搜索)
 */
firstAgentName?: string;
/**
 * 二级代理名称 (模糊搜索)
 */
secondAgentName?: string;
/**
 * 三级代理名称 (模糊搜索)
 */
thirdAgentName?: string;
/**
 * Telegram ID (模糊搜索)
 */
telegramId?: string;
/**
 * Telegram用户名 (模糊搜索)
 */
telegramUsername?: string;
/**
 * 真实姓名 (模糊搜索)
 */
firstName?: string;
/**
 * 租户用户名 (模糊搜索)
 */
tenantUsername?: string;
/**
 * 租户业务名称 (模糊搜索)
 */
businessName?: string;
};
