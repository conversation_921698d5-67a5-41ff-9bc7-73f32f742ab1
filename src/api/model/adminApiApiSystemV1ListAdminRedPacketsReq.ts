/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */
import type { AdminApiApiSystemV1ListAdminRedPacketsReqStatus } from './adminApiApiSystemV1ListAdminRedPacketsReqStatus';
import type { AdminApiApiSystemV1ListAdminRedPacketsReqType } from './adminApiApiSystemV1ListAdminRedPacketsReqType';

export interface AdminApiApiSystemV1ListAdminRedPacketsReq {
  /** @minimum 1 */
  page: number;
  /** @minimum 1 */
  pageSize: number;
  creatorUserId?: number;
  tokenId?: number;
  /** 红包状态: active-可领取, expired-已过期, empty-已领完, cancelled-已取消 */
  status?: AdminApiApiSystemV1ListAdminRedPacketsReqStatus;
  /** 红包类型: random-随机金额, fixed-固定金额 */
  type?: AdminApiApiSystemV1ListAdminRedPacketsReqType;
  /** 创建时间范围，格式：2025-01-01,2025-01-31 */
  dateRange?: string;
  export?: boolean;
  senderAccount?: string;
  senderNickname?: string;
  symbol?: string;
  /** 红包UUID (模糊搜索) */
  uuid?: string;
  /** 一级代理名称 (模糊搜索) */
  firstAgentName?: string;
  /** 二级代理名称 (模糊搜索) */
  secondAgentName?: string;
  /** 三级代理名称 (模糊搜索) */
  thirdAgentName?: string;
  /** Telegram ID (模糊搜索) */
  telegramId?: string;
  /** Telegram用户名 (模糊搜索) */
  telegramUsername?: string;
  /** 真实姓名 (模糊搜索) */
  firstName?: string;
  /** 租户用户名 (模糊搜索) */
  tenantUsername?: string;
  /** 租户业务名称 (模糊搜索) */
  businessName?: string;
}
