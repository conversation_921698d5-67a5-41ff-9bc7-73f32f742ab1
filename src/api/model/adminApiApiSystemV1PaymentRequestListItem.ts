/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */

export interface AdminApiApiSystemV1PaymentRequestListItem {
  /** 收款请求ID */
  requestId?: number;
  /** 收款发起者用户ID */
  requesterUserId?: number;
  /** 收款发起者用户名 */
  requesterUsername?: string;
  /** 收款发起者账号 */
  requesterAccount?: string;
  /** 付款人用户ID */
  payerUserId?: number;
  /** 付款人用户名 */
  payerUsername?: string;
  /** 付款人账号 */
  payerAccount?: string;
  /** 代币ID */
  tokenId?: number;
  /** 代币符号 */
  tokenSymbol?: string;
  /** 代币名称 */
  tokenName?: string;
  /** 收款金额 */
  amount?: string;
  /** 收款说明/备注 */
  memo?: string;
  /** 状态 (1:待支付, 2:已支付, 3:已过期, 4:已取消) */
  status?: number;
  /** 状态文本 */
  statusText?: string;
  /** 创建时间 */
  createdAt?: string;
  /** 过期时间 */
  expiresAt?: string;
  /** 发起者一级代理名称 */
  firstAgentName?: string;
  /** 发起者二级代理名称 */
  secondAgentName?: string;
  /** 发起者三级代理名称 */
  thirdAgentName?: string;
  /** 发起者Telegram ID */
  telegramId?: string;
  /** 发起者Telegram用户名 */
  telegramUsername?: string;
  /** 发起者真实姓名 */
  firstName?: string;
  /** 发起者租户用户名 */
  tenantUsername?: string;
  /** 发起者租户业务名称 */
  tenantBusinessName?: string;
  /** 发起者租户名称 */
  tenantName?: string;
  /** 发起者租户状态 (0-禁用, 1-启用) */
  tenantStatus?: number;
  /** 付款人一级代理名称 */
  payerFirstAgentName?: string;
  /** 付款人二级代理名称 */
  payerSecondAgentName?: string;
  /** 付款人三级代理名称 */
  payerThirdAgentName?: string;
  /** 付款人Telegram ID */
  payerTelegramId?: string;
  /** 付款人Telegram用户名 */
  payerTelegramUsername?: string;
  /** 付款人真实姓名 */
  payerFirstName?: string;
  /** 付款人租户用户名 */
  payerTenantUsername?: string;
  /** 付款人租户业务名称 */
  payerTenantBusinessName?: string;
  /** 付款人租户名称 */
  payerTenantName?: string;
  /** 付款人租户状态 (0-禁用, 1-启用) */
  payerTenantStatus?: number;
}
