/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */
import type { GithubComShopspringDecimalDecimal } from './githubComShopspringDecimalDecimal';

export interface AdminApiApiSystemV1ReferralCommissionListItem {
  /** 佣金记录 ID (主键) */
  commissionId?: number;
  /** 租户 id */
  tenantId?: number;
  /** 关联的触发佣金的交易 ID (例如: 下级的某笔操作, 外键指向 transactions.transaction_id) */
  transactionId?: number;
  /** 获得佣金的用户 ID (外键, 指向 users.user_id) */
  referrerId?: number;
  /** 产生佣金的被推荐人用户 ID (外键, 指向 users.user_id) */
  inviteeId?: number;
  /** 佣金产生的推荐层级 */
  level?: number;
  /** 佣金金额 */
  commissionAmount?: GithubComShopspringDecimalDecimal;
  /** 佣金比率 (例如: 0.01 表示 1%) */
  commissionRate?: GithubComShopspringDecimalDecimal;
  /** 佣金代币 ID (外键, 指向 tokens.token_id) */
  tokenId?: number;
  /** 佣金状态: pending-待发放, paid-已发放, cancelled-已取消 */
  status?: string;
  /** 创建时间 */
  createdAt?: string;
  /** 最后更新时间 */
  updatedAt?: string;
  /** 软删除时间 */
  deletedAt?: string;
  /** 推荐人用户名 */
  referrerUsername?: string;
  /** 推荐人账号 */
  referrerAccount?: string;
  /** 被推荐人用户名 */
  inviteeUsername?: string;
  /** 被推荐人账号 */
  inviteeAccount?: string;
  /** 代币符号 */
  tokenSymbol?: string;
  /** 推荐人一级代理名称 */
  referrerFirstAgentName?: string;
  /** 推荐人二级代理名称 */
  referrerSecondAgentName?: string;
  /** 推荐人三级代理名称 */
  referrerThirdAgentName?: string;
  /** 推荐人主Telegram ID */
  referrerTelegramId?: string;
  /** 推荐人主Telegram用户名 */
  referrerTelegramUsername?: string;
  /** 推荐人主备份账户名字 */
  referrerFirstName?: string;
  /** 被推荐人一级代理名称 */
  inviteeFirstAgentName?: string;
  /** 被推荐人二级代理名称 */
  inviteeSecondAgentName?: string;
  /** 被推荐人三级代理名称 */
  inviteeThirdAgentName?: string;
  /** 被推荐人主Telegram ID */
  inviteeTelegramId?: string;
  /** 被推荐人主Telegram用户名 */
  inviteeTelegramUsername?: string;
  /** 被推荐人主备份账户名字 */
  inviteeFirstName?: string;
  /** 推荐人租户用户名 */
  referrerTenantUsername?: string;
  /** 推荐人租户业务名称 */
  referrerTenantBusinessName?: string;
  /** 推荐人租户名称 */
  referrerTenantName?: string;
  /** 推荐人租户状态 (0-禁用, 1-启用) */
  referrerTenantStatus?: number;
  /** 被推荐人租户用户名 */
  inviteeTenantUsername?: string;
  /** 被推荐人租户业务名称 */
  inviteeTenantBusinessName?: string;
  /** 被推荐人租户名称 */
  inviteeTenantName?: string;
  /** 被推荐人租户状态 (0-禁用, 1-启用) */
  inviteeTenantStatus?: number;
}
