/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */
import type { GithubComShopspringDecimalDecimal } from './githubComShopspringDecimalDecimal';

export type PutApiSystemExchangeProductsProductIdBody = {
  /** 计价代币 */
  quoteToken?: string;
  /**
   * 交易对符号
   * @minLength 1
   * @maxLength 50
   */
  symbol?: string;
  /** 产品类型 */
  productType?: string;
  /** 单笔最大基础代币数量 */
  maxBaseAmountPerTx?: GithubComShopspringDecimalDecimal;
  /** 价格刷新间隔(秒) */
  rateRefreshIntervalSec?: number;
  /** 显示顺序 */
  displayOrder?: number;
  /** 允许买入 */
  allowBuy?: number;
  /** 每日基础代币限额 */
  dailyBaseVolumeLimit?: GithubComShopspringDecimalDecimal;
  /** 价差率 */
  spreadRate?: GithubComShopspringDecimalDecimal;
  /** 最小手续费金额（以输出代币计价） */
  minOutputFeeAmount?: GithubComShopspringDecimalDecimal;
  /** 是否激活 */
  isActive?: number;
  /** 维护信息 */
  maintenanceMessage?: string;
  /** 单笔最小基础代币数量 */
  minBaseAmountPerTx?: GithubComShopspringDecimalDecimal;
  /** 累计基础代币限额 */
  totalBaseVolumeLimit?: GithubComShopspringDecimalDecimal;
  /** 输出代币手续费率 */
  outputFeeRate?: GithubComShopspringDecimalDecimal;
  /** 描述 */
  description?: string;
  /** 基础代币 */
  baseToken?: string;
  /** 允许卖出 */
  allowSell?: number;
  /** 价格源 */
  priceSource?: string;
  /** 允许滑点百分比 */
  allowedSlippagePercent?: GithubComShopspringDecimalDecimal;
  /** 手续费策略 */
  feeStrategy?: string;
};
