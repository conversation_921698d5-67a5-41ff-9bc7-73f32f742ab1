/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */

export interface AdminApiApiSystemV1GetAdminTransferDetailRes {
  /** 转账记录ID */
  transferId?: number;
  /** 发送方用户ID */
  senderUserId?: number;
  /** 发送方用户名 */
  senderUsername?: string;
  /** 发送方账户 */
  senderAccount?: string;
  /** 接收方用户ID */
  receiverUserId?: number;
  /** 接收方用户名 */
  receiverUsername?: string;
  /** 接收方账户 */
  receiverAccount?: string;
  /** 代币ID */
  tokenId?: number;
  /** 代币符号 */
  tokenSymbol?: string;
  /** 代币小数位数 */
  tokenDecimals?: number;
  /** 转账金额(格式化后) */
  amountStr?: string;
  /** 转账备注 */
  memo?: string;
  /** 发送方交易ID */
  senderTxId?: number;
  /** 接收方交易ID */
  receiverTxId?: number;
  /** 创建时间 */
  createdAt?: string;
  /** 消息ID */
  messageId?: number;
  /** 聊天ID */
  chatId?: number;
  /** 转账状态 */
  status?: string;
  /** 冻结ID */
  holdId?: string;
  /** 过期时间 */
  expiresAt?: string;
  /** 更新时间 */
  updatedAt?: string;
  /** 是否需要密码 */
  needPass?: boolean;
  /** 转账密钥/唯一标识 */
  key?: string;
  /** 转账记录的币种符号 */
  transferSymbol?: string;
  /** 消息内容 */
  message?: string;
  /** 内联消息ID */
  inlineMessageId?: string;
  /** 发送方一级代理名称 */
  firstAgentName?: string;
  /** 发送方二级代理名称 */
  secondAgentName?: string;
  /** 发送方三级代理名称 */
  thirdAgentName?: string;
  /** 发送方Telegram ID */
  telegramId?: string;
  /** 发送方Telegram用户名 */
  telegramUsername?: string;
  /** 发送方备份账户名字 */
  firstName?: string;
  /** 接收方一级代理名称 */
  receiverFirstAgentName?: string;
  /** 接收方二级代理名称 */
  receiverSecondAgentName?: string;
  /** 接收方三级代理名称 */
  receiverThirdAgentName?: string;
  /** 接收方Telegram ID */
  receiverTelegramId?: string;
  /** 接收方Telegram用户名 */
  receiverTelegramUsername?: string;
  /** 接收方备份账户名字 */
  receiverFirstName?: string;
  /** 发送方租户用户名 */
  senderTenantUsername?: string;
  /** 发送方租户业务名称 */
  senderTenantBusinessName?: string;
  /** 发送方租户名称 */
  senderTenantName?: string;
  /** 发送方租户状态 (0-禁用, 1-启用) */
  senderTenantStatus?: number;
  /** 接收方租户用户名 */
  receiverTenantUsername?: string;
  /** 接收方租户业务名称 */
  receiverTenantBusinessName?: string;
  /** 接收方租户名称 */
  receiverTenantName?: string;
  /** 接收方租户状态 (0-禁用, 1-启用) */
  receiverTenantStatus?: number;
}
