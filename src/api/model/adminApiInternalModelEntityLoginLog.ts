/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */
import type { GithubComGogfGfV2EncodingGjsonJson } from './githubComGogfGfV2EncodingGjsonJson';

export interface AdminApiInternalModelEntityLoginLog {
  /** 日志ID */
  id?: number;
  /** 租户 id */
  tenantId?: number;
  /** 请求ID */
  reqId?: string;
  /** 用户类型 */
  memberType?: string;
  /** 用户ID */
  memberId?: number;
  /** 用户名 */
  username?: string;
  /** 响应数据 */
  response?: GithubComGogfGfV2EncodingGjsonJson;
  /** 登录时间 */
  loginAt?: string;
  /** 登录IP */
  loginIp?: string;
  /** 省编码 */
  provinceId?: number;
  /** 市编码 */
  cityId?: number;
  /** UA信息 */
  userAgent?: string;
  /** 错误提示 */
  errMsg?: string;
  /** 状态 */
  status?: number;
  /** 创建时间 */
  createdAt?: string;
  /** 修改时间 */
  updatedAt?: string;
  /** 软删除的时间戳 */
  deletedAt?: string;
}
