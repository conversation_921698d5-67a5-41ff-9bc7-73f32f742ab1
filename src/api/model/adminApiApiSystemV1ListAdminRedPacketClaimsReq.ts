/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */
import type { AdminApiApiSystemV1ListAdminRedPacketClaimsReqStatus } from './adminApiApiSystemV1ListAdminRedPacketClaimsReqStatus';
import type { AdminApiApiSystemV1ListAdminRedPacketClaimsReqRedPacketType } from './adminApiApiSystemV1ListAdminRedPacketClaimsReqRedPacketType';

export interface AdminApiApiSystemV1ListAdminRedPacketClaimsReq {
  /** @minimum 1 */
  page: number;
  /** @minimum 1 */
  pageSize: number;
  redPacketId?: number;
  /** 红包UUID (模糊搜索) */
  redPacketUuid?: string;
  claimerUserId?: number;
  claimerUsername?: string;
  senderUserId?: number;
  senderUsername?: string;
  receiverUserId?: number;
  receiverUsername?: string;
  /** 状态: pending-待领取, claimed-已领取, cancelled-已取消 */
  status?: AdminApiApiSystemV1ListAdminRedPacketClaimsReqStatus;
  symbol?: string;
  /** 红包类型: random-随机金额, fixed-固定金额 */
  redPacketType?: AdminApiApiSystemV1ListAdminRedPacketClaimsReqRedPacketType;
  minAmount?: number;
  maxAmount?: number;
  /** 创建时间范围，格式：2025-01-01,2025-01-31 */
  dateRange?: string;
  export?: boolean;
  /** 领取方一级代理名称 (模糊搜索) */
  firstAgentName?: string;
  /** 领取方二级代理名称 (模糊搜索) */
  secondAgentName?: string;
  /** 领取方三级代理名称 (模糊搜索) */
  thirdAgentName?: string;
  /** 领取方Telegram ID (模糊搜索) */
  telegramId?: string;
  /** 领取方Telegram用户名 (模糊搜索) */
  telegramUsername?: string;
  /** 领取方真实姓名 (模糊搜索) */
  firstName?: string;
  /** 发送方一级代理名称 (模糊搜索) */
  senderFirstAgentName?: string;
  /** 发送方二级代理名称 (模糊搜索) */
  senderSecondAgentName?: string;
  /** 发送方三级代理名称 (模糊搜索) */
  senderThirdAgentName?: string;
  /** 发送方Telegram ID (模糊搜索) */
  senderTelegramId?: string;
  /** 发送方Telegram用户名 (模糊搜索) */
  senderTelegramUsername?: string;
  /** 发送方真实姓名 (模糊搜索) */
  senderFirstName?: string;
  /** 领取方租户用户名 (模糊搜索) */
  tenantUsername?: string;
  /** 领取方租户业务名称 (模糊搜索) */
  businessName?: string;
  /** 发送方租户用户名 (模糊搜索) */
  senderTenantUsername?: string;
  /** 发送方租户业务名称 (模糊搜索) */
  senderBusinessName?: string;
}
