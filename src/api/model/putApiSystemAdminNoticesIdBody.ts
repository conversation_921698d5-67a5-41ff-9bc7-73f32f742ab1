/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */
import type { PutApiSystemAdminNoticesIdBodyTag } from './putApiSystemAdminNoticesIdBodyTag';
import type { GithubComGogfGfV2EncodingGjsonJson } from './githubComGogfGfV2EncodingGjsonJson';
import type { PutApiSystemAdminNoticesIdBodyStatus } from './putApiSystemAdminNoticesIdBodyStatus';
import type { PutApiSystemAdminNoticesIdBodyType } from './putApiSystemAdminNoticesIdBodyType';

export type PutApiSystemAdminNoticesIdBody = {
  /**
   * 备注
   * @maxLength 200
   */
  remark?: string;
  /**
   * 排序
   * @minimum 0
   */
  sort?: number;
  /**
   * 公告标题
   * @minLength 1
   * @maxLength 100
   */
  title: string;
  /** 标签(0:普通, 1:重要, 2:活动) */
  tag?: PutApiSystemAdminNoticesIdBodyTag;
  /** 接收者ID列表 (类型为私信时必填, JSON数组 [1, 2, 3]) */
  receiver?: GithubComGogfGfV2EncodingGjsonJson;
  /** 公告状态(0:草稿, 1:发布, 2:停用) */
  status: PutApiSystemAdminNoticesIdBodyStatus;
  /** 公告类型(1:通知, 2:私信) */
  type: PutApiSystemAdminNoticesIdBodyType;
  /** 公告内容 (HTML) */
  content: string;
};
