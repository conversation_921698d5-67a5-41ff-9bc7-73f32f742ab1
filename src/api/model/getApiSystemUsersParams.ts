/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */

export type GetApiSystemUsersParams = {
/**
 * 页码
 */
page?: number;
/**
 * 每页数量
 */
pageSize?: number;
/**
 * 账号 (模糊搜索)
 */
account?: string;
/**
 * 邮箱 (模糊搜索)
 */
email?: string;
/**
 * 手机号 (模糊搜索)
 */
phone?: string;
/**
 * 账户类型 (0-未知, 1-普通用户, 2-商户, 3-代理)
 */
accountType?: number;
/**
 * 状态 (true-启用, false-禁用)
 */
isStop?: boolean;
/**
 * 推荐人ID
 */
recommendId?: number;
/**
 * 创建时间范围，格式：2025-01-01,2025-01-31
 */
dateRange?: string;
/**
 * 一级代理名称 (模糊搜索)
 */
firstAgentName?: string;
/**
 * 二级代理名称 (模糊搜索)
 */
secondAgentName?: string;
/**
 * 三级代理名称 (模糊搜索)
 */
thirdAgentName?: string;
/**
 * 是否导出 (true-导出, false-不导出)
 */
export?: boolean;
/**
 * Telegram ID (模糊搜索)
 */
telegramId?: string;
/**
 * Telegram用户名 (模糊搜索)
 */
telegramUsername?: string;
/**
 * 真实姓名 (模糊搜索)
 */
firstName?: string;
/**
 * 租户用户名 (模糊搜索)
 */
tenantUsername?: string;
/**
 * 租户业务名称 (模糊搜索)
 */
businessName?: string;
};
