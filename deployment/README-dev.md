# 开发环境 Docker 配置

这个目录包含了用于开发环境的 Docker 配置文件。

## 文件说明

- `Dockerfile.dev` - 开发环境的 Dockerfile
- `docker-compose.dev.yml` - 开发环境的 Docker Compose 配置
- `start-dev.sh` - 启动开发环境的便捷脚本

## 使用方法

### 方法一：使用启动脚本（推荐）

```bash
# 在项目根目录执行
./deployment/start-dev.sh
```

### 方法二：直接使用 docker-compose

```bash
# 在项目根目录执行
docker-compose -f deployment/docker-compose.dev.yml up --build
```

### 停止开发环境

```bash
# 停止容器
docker-compose -f deployment/docker-compose.dev.yml down

# 停止容器并删除 volumes（清理 node_modules）
docker-compose -f deployment/docker-compose.dev.yml down -v
```

## 特性

1. **热重载**：本地代码变更会自动反映到容器中
2. **端口映射**：容器的 80 端口映射到主机的 80 端口
3. **Volume 挂载**：
   - 整个项目目录挂载到容器的 `/app` 目录
   - `node_modules` 使用独立的 volume，避免本地和容器的依赖冲突
4. **开发环境变量**：自动设置开发环境相关的环境变量

## 访问应用

启动成功后，可以通过以下地址访问应用：

- 开发服务器：http://localhost:80

## 注意事项

1. 首次启动可能需要较长时间来安装依赖
2. 如果遇到依赖问题，可以删除 volume 重新构建：
   ```bash
   docker-compose -f deployment/docker-compose.dev.yml down -v
   docker-compose -f deployment/docker-compose.dev.yml up --build
   ```
3. 开发环境和生产环境使用不同的配置文件，互不影响
