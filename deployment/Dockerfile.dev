# Development Dockerfile
FROM node:18-alpine

WORKDIR /app

# Install pnpm globally
RUN npm install -g pnpm

# Copy package files first for better caching
COPY package.json pnpm-lock.yaml ./

# Install dependencies
RUN pnpm install

# Expose the development port
EXPOSE 80

# Set environment variables for development
ENV NODE_ENV=development
ENV PORT=80
ENV UMI_ENV=development

# Default command to start development server
CMD ["pnpm", "start"]
