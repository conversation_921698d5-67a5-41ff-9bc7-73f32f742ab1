# Build stage
FROM node:18-alpine AS build

WORKDIR /app

# No need for Docker build flag anymore

# Copy package.json and pnpm-lock.yaml
COPY package.json pnpm-lock.yaml ./

# Install pnpm
RUN npm install -g pnpm

# Install dependencies
RUN pnpm install
# Prebuild to generate necessary files
RUN pnpm prebuild
# Copy source code
COPY . .

# Build the application - will use .env.development
RUN pnpm run build

# Production stage
FROM nginx:alpine

# Install jq for JSON parsing in entrypoint script
RUN apk add --no-cache jq bash gettext

# Copy the build output to replace the default nginx contents
COPY --from=build /app/dist /usr/share/nginx/html

# Copy custom nginx config
COPY deployment/nginx.conf /etc/nginx/conf.d/default.conf


# Expose port 80
EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]