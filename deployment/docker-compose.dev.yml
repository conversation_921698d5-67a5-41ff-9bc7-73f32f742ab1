services:
  admin-web:
    build:
      context: ..
      dockerfile: deployment/Dockerfile.dev
    ports:
      - '81:80'   # Map development port
    volumes:
      # Mount the entire project directory to /app
      - ..:/app
      # Use named volume for node_modules to avoid conflicts
      - node_modules:/app/node_modules
    environment:
      - NODE_ENV=development
      - PORT=80
      - UMI_ENV=development
    container_name: xpay-admin-web-dev
    restart: unless-stopped
    # Enable hot reload by mounting source code
    stdin_open: true
    tty: true
    networks:
      - xpay_app-network
volumes:
  node_modules:

networks:
  xpay_app-network:
    external: true
